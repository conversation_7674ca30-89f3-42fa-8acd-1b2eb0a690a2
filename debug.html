<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - 设计对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }



        .comparison-container {
            display: flex;
            height: 100vh;
            min-height: 1000px;
        }

        .left-section {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background: #fafafa;
        }



        .right-section {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background: #fafafa;
        }

        .phone-mockup {
            position: relative;
            width: 470px;
            height: 972px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 50px;
            padding: 20px;
            box-shadow:
                0 0 0 2px #333,
                0 0 0 4px #1a1a1a,
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.1);
        }

        .phone-mockup::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 30px;
            background: #1a1a1a;
            border-radius: 15px;
            z-index: 2;
        }

        .phone-mockup::after {
            content: '';
            position: absolute;
            top: 22px;
            left: 50%;
            transform: translateX(-50%);
            width: 15px;
            height: 15px;
            background: #333;
            border-radius: 50%;
            z-index: 3;
        }

        .phone-screen {
            width: 430px;
            height: 932px;
            background: #000;
            border-radius: 35px;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .design-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 0;
        }

        .phone-buttons {
            position: absolute;
            right: -3px;
            top: 120px;
        }

        .phone-button {
            width: 3px;
            background: #333;
            margin-bottom: 15px;
            border-radius: 2px;
        }

        .phone-button.volume-up {
            height: 30px;
        }

        .phone-button.volume-down {
            height: 30px;
        }

        .phone-button.power {
            position: absolute;
            right: 0;
            top: 80px;
            height: 50px;
        }

        .implementation-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            background: white;
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .controls input[type="file"] {
            margin-bottom: 10px;
            padding: 5px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
        }

        .controls input[type="url"] {
            width: 200px;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .controls button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 5px;
        }

        .controls button:hover {
            background: #5856eb;
        }

        .placeholder {
            text-align: center;
            color: #6b7280;
            font-size: 16px;
        }

        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.5;
        }

        .phone-placeholder {
            color: #666;
            font-size: 14px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">

        
        <div class="comparison-container">
            <div class="left-section">
                <div class="phone-mockup">
                    <div class="phone-screen">
                        <div id="design-placeholder" class="phone-placeholder">
                            <div style="font-size: 32px; margin-bottom: 10px;">📱</div>
                            <p>拖拽或上传设计稿</p>
                        </div>
                        <img id="design-image" class="design-image" style="display: none;" alt="设计稿">
                    </div>
                    <div class="phone-buttons">
                        <div class="phone-button volume-up"></div>
                        <div class="phone-button volume-down"></div>
                        <div class="phone-button power"></div>
                    </div>
                </div>
            </div>

            <div class="right-section">
                <div id="iframe-placeholder" class="placeholder">
                    <div class="placeholder-icon">🌐</div>
                    <p>请输入页面URL</p>
                </div>
                <iframe id="implementation-iframe" class="implementation-iframe" style="display: none;"></iframe>
            </div>
        </div>
    </div>

    <div class="controls">
        <div>
            <input type="file" id="image-upload" accept="image/*" placeholder="选择设计稿图片">
        </div>
        <div>
            <input type="url" id="url-input" placeholder="输入页面URL (如: http://localhost:3000)">
            <button onclick="loadPage()">加载页面</button>
        </div>
        <div>
            <button onclick="refreshIframe()">刷新页面</button>
        </div>
    </div>

    <script>
        // 处理图片上传
        document.getElementById('image-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.getElementById('design-image');
                    const placeholder = document.getElementById('design-placeholder');
                    
                    img.src = e.target.result;
                    img.style.display = 'block';
                    placeholder.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });

        // 加载页面到iframe
        function loadPage() {
            const url = document.getElementById('url-input').value;
            if (url) {
                const iframe = document.getElementById('implementation-iframe');
                const placeholder = document.getElementById('iframe-placeholder');
                
                iframe.src = url;
                iframe.style.display = 'block';
                placeholder.style.display = 'none';
            } else {
                alert('请输入有效的URL');
            }
        }

        // 刷新iframe
        function refreshIframe() {
            const iframe = document.getElementById('implementation-iframe');
            if (iframe.src) {
                iframe.src = iframe.src;
            }
        }

        // 支持拖拽上传图片
        const designPanel = document.querySelector('.phone-screen');
        
        designPanel.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.backgroundColor = '#f0f9ff';
        });

        designPanel.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.backgroundColor = '#fafafa';
        });

        designPanel.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.backgroundColor = '#fafafa';
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.getElementById('design-image');
                    const placeholder = document.getElementById('design-placeholder');
                    
                    img.src = e.target.result;
                    img.style.display = 'block';
                    placeholder.style.display = 'none';
                };
                reader.readAsDataURL(files[0]);
            }
        });

        // URL输入框回车事件
        document.getElementById('url-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadPage();
            }
        });
    </script>
</body>
</html>
